import { createRouter, createWebHistory } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { ElMessage } from 'element-plus';

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('./pages/Home/index.vue'),
    meta: {
      title: '首页',
    },
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('./pages/Login/index.vue'),
    meta: {
      title: '登录注册',
    },
  },
  {
    path: '/download',
    name: 'Download',
    component: () => import('./pages/Download/index.vue'),
    meta: {
      title: '下载',
    },
  },
  {
    path: '/forgetPwd',
    name: 'ForgetPwd',
    component: () => import('./pages/ForgetPwd/index.vue'),
    meta: {
      title: '忘记密码',
    },
  },
  {
    path: '/goods',
    name: 'Goods',
    component: () => import('./pages/Goods/list.vue'),
    meta: {
      title: '商品列表',
    },
  },
  {
    path: '/goods/:id',
    name: 'GoodsDetail',
    component: () => import('./pages/Goods/index.vue'),
    meta: {
      title: '商品详情',
    },
  },
  {
    path: '/wish',
    name: 'Wish',
    component: () => import('./pages/Wish/index.vue'),
    meta: {
      title: '心愿单',
      requiresAuth: false,
    },
  },
  {
    path: '/confirm',
    name: 'Confirm',
    component: () => import('./pages/Confirm/index.vue'),
    meta: {
      title: '确认订单',
      // requiresAuth: true,
    },
  },
  {
    path: '/pay',
    name: 'Pay',
    component: () => import('./pages/Pay/index.vue'),
    meta: {
      title: '支付页面',
      requiresAuth: false,
    },
  },
  {
    path: '/payment/success',
    name: 'PaymentSuccess',
    component: () => import('./pages/PaymentSuccess/index.vue'),
    meta: {
      title: '支付成功',
      requiresAuth: false,
    },
  },
  {
    path: '/payment/test',
    name: 'PaymentTest',
    component: () => import('./pages/PaymentTest/index.vue'),
    meta: {
      title: '支付测试',
      requiresAuth: false,
    },
  },
  {
    path: '/mine',
    name: 'Mine',
    redirect: '/center',
    component: () => import('./pages/Mine/index.vue'),
    meta: {
      title: '我的',
      requiresAuth: false,
    },
    children: [
      {
        path: '/center',
        name: 'Center',
        component: () => import('./pages/Mine/center.vue'),
        meta: {
          title: '我的订单',
        },
      },
      {
        path: '/order',
        name: 'Order',
        component: () => import('./pages/Mine/order.vue'),
        meta: {
          title: '我的订单',
        },
      },
      {
        path: '/collect',
        name: 'Collect',
        component: () => import('./pages/Mine/collect.vue'),
        meta: {
          title: '我的收藏',
        },
      },
      {
        path: '/pwd',
        name: 'Pwd',
        component: () => import('./pages/Mine/pwd.vue'),
        meta: {
          title: '我的收藏',
        },
      },
      {
        path: '/user',
        name: 'User',
        component: () => import('./pages/Mine/user.vue'),
        meta: {
          title: '个人中心',
        },
      },
      {
        path: '/address',
        name: 'Address',
        component: () => import('./pages/Mine/address.vue'),
        meta: {
          title: '我的地址',
        },
      },
      {
        path: '/aftersale',
        name: 'Aftersale',
        component: () => import('./pages/Mine/aftersale.vue'),
        meta: {
          title: '我的售后',
        },
      },
      {
        path: '/orderDetail',
        name: 'OrderDetail',
        component: () => import('./pages/Mine/orderDetail.vue'),
        meta: {
          title: '订单详情',
        },
      },
    ],
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('./pages/NotFound.vue'),
    meta: {
      title: '页面未找到',
    },
  },
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  // 路由切换时滚动到顶部
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
});

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title;
  }

  // 获取用户状态
  const userStore = useUserStore();
  const isLoggedIn = userStore.isLoggedIn;

  // 需要登录的路由
  const requiresAuth = to.matched.some((record) => record.meta.requiresAuth);

  // 如果需要登录但未登录，跳转到登录页
  if (requiresAuth && !isLoggedIn) {
    ElMessage.warning('请先登录');
    next({
      path: '/login',
      query: { redirect: to.fullPath }, // 保存目标路径，登录后可以重定向
    });
    return;
  }

  // 如果已登录访问登录页，跳转到首页
  if (to.path === '/login' && isLoggedIn) {
    next('/');
    return;
  }

  next();
});

// 全局后置钩子
router.afterEach((to, from) => {
  // 路由切换完成后的逻辑
  // 例如：埋点统计、页面加载完成事件等
  console.log(`路由切换: ${from.path} -> ${to.path}`);
});

export default router;
