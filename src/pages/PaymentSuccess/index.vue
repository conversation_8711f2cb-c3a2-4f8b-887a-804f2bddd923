<template>
    <div class="payment-success">
        <div class="payment-success-container">
            <div class="payment-success-icon">
                <img src="@assets/checked.png" alt="支付成功" />
            </div>
            <h2 class="payment-success-title">支付成功！</h2>
            <div class="payment-success-info">
                <p>订单号：{{ orderNumber }}</p>
                <p>支付方式：{{ paymentMethod }}</p>
                <p>支付金额：{{ amount }}</p>
                <p>支付时间：{{ paymentTime }}</p>
            </div>
            <div class="payment-success-actions">
                <button @click="goToOrders" class="btn-primary">查看订单</button>
                <button @click="goToHome" class="btn-secondary">返回首页</button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

const orderNumber = ref('');
const paymentMethod = ref('');
const amount = ref('');
const paymentTime = ref('');

onMounted(() => {
    // 从URL参数获取支付信息
    orderNumber.value = route.query.orderNumber || '';
    paymentMethod.value = route.query.paymentMethod || '';
    amount.value = route.query.amount || '';
    paymentTime.value = new Date().toLocaleString();
});

const goToOrders = () => {
    router.push('/orders');
};

const goToHome = () => {
    router.push('/');
};
</script>

<style lang="scss" scoped>
.payment-success {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem;

    .payment-success-container {
        background: white;
        border-radius: 1.6rem;
        padding: 4rem 3rem;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        max-width: 50rem;
        width: 100%;

        .payment-success-icon {
            margin-bottom: 2rem;

            img {
                width: 8rem;
                height: 8rem;
            }
        }

        .payment-success-title {
            color: #2c3e50;
            font-size: 2.8rem;
            font-weight: 600;
            margin-bottom: 3rem;
        }

        .payment-success-info {
            background: #f8f9fa;
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 3rem;

            p {
                margin: 1rem 0;
                font-size: 1.6rem;
                color: #495057;
                display: flex;
                justify-content: space-between;
                align-items: center;

                &:first-child {
                    margin-top: 0;
                }

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

        .payment-success-actions {
            display: flex;
            gap: 1.5rem;
            justify-content: center;

            button {
                padding: 1.2rem 2.4rem;
                border: none;
                border-radius: 0.8rem;
                font-size: 1.6rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                min-width: 12rem;

                &.btn-primary {
                    background: #6e4aeb;
                    color: white;

                    &:hover {
                        background: #5a3cd0;
                        transform: translateY(-2px);
                        box-shadow: 0 8px 16px rgba(110, 74, 235, 0.3);
                    }
                }

                &.btn-secondary {
                    background: #e9ecef;
                    color: #495057;

                    &:hover {
                        background: #dee2e6;
                        transform: translateY(-2px);
                        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
                    }
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .payment-success {
        padding: 1rem;

        .payment-success-container {
            padding: 3rem 2rem;

            .payment-success-title {
                font-size: 2.4rem;
            }

            .payment-success-actions {
                flex-direction: column;

                button {
                    width: 100%;
                }
            }
        }
    }
}
</style>
