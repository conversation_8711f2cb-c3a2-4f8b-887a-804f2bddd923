<template>
    <div class="login_view">
        <p class="login_view_title">首页 / 用户登录与注册</p>
        <div class="login_view_main">
            <div class="login_view_main_form">
                <div class="login_view_main_form_title">{{ t('login.login') }}</div>
                <div class="login_view_main_form_box mb12">
                    <div class="login_view_main_form_box_input">
                        <input class="login_view_main_form_box_input_val" :placeholder="t('login.placeholder')"
                            v-model="login.email" type="text" />
                    </div>
                    <div class="login_view_main_form_box_input">
                        <input class="login_view_main_form_box_input_val" :placeholder="t('login.password')"
                            v-model="login.password" type="password" />
                    </div>
                </div>
                <div class="login_view_main_form_tip">
                    <img src="@assets/help.png" class="login_view_main_form_tip_icon" />
                    {{ t('login.forget') }}
                </div>
                <div class="login_view_main_form_btn" @click="doLogin()">{{ t('login.login') }}</div>
            </div>
            <div class="login_view_main_form">
                <div class="login_view_main_form_title">{{ t('login.register') }}</div>
                <div class="login_view_main_form_box">
                    <div class="login_view_main_form_box_input">
                        <input class="login_view_main_form_box_input_val" :placeholder="t('login.placeholder')"
                            v-model="register.email" type="text" />
                    </div>
                    <div class="login_view_main_form_box_input">
                        <input class="login_view_main_form_box_input_val" :placeholder="t('login.password')"
                            v-model="register.password" type="password" />
                    </div>
                </div>
                <div class="login_view_main_form_btn" @click="doRegister()">{{ t('login.register') }}</div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { login, register } from '@api/auth';
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';
const { t, locale } = useI18n();
const router = useRouter();

const register = reactive({
    email: '',
    password: ''
});

const login = reactive({
    email: '',
    password: ''
});

const doLogin = () => {
    if (!login.email || !login.password) {
        ElMessage({
            message: '请输入邮箱和密码',
            type: 'warning'
        });
        return;
    }
    let emailReg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
    if (!emailReg.test(login.email)) {
        ElMessage({
            message: '请输入正确的邮箱',
            type: 'warning'
        });
        return;
    }
    login({
        email: login.email,
        password: login.password
    }).then(res => {
        router.replace('/');
        console.log('res', res);
    }).catch(error => {
        console.error('登录失败:', error);
    })
}

const doRegister = () => {
    if (!register.email || !register.password) {
        ElMessage({
            message: '请输入邮箱和密码',
            type: 'warning'
        });
        return;
    }
    let emailReg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
    if (!emailReg.test(register.email)) {
        ElMessage({
            message: '请输入正确的邮箱',
            type: 'warning'
        });
        return;
    }
    register({
        email: register.email,
        password: register.password
    }).then(res => {
        router.replace('/');
        console.log('res', res);
    }).catch(error => {
        console.error('注册失败:', error);
    })
}
</script>
<style lang="scss" scoped>
@import url('./index.scss');
</style>