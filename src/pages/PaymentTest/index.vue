<template>
    <div class="payment-test">
        <div class="payment-test-container">
            <h2>支付方式测试页面</h2>
            
            <div class="test-info">
                <p><strong>测试订单号:</strong> {{ testOrderNumber }}</p>
                <p><strong>测试金额:</strong> ${{ testAmount }}</p>
            </div>
            
            <div class="payment-methods">
                <h3>选择支付方式进行测试</h3>
                <div class="payment-grid">
                    <div 
                        v-for="payment in paymentMethods" 
                        :key="payment.id"
                        class="payment-method-card"
                        @click="testPayment(payment.id)"
                    >
                        <img :src="payment.icon" :alt="payment.name" class="payment-icon" />
                        <h4>{{ payment.name }}</h4>
                        <p>{{ payment.description }}</p>
                        <button class="test-button">测试支付</button>
                    </div>
                </div>
            </div>
            
            <div class="test-results" v-if="testResults.length > 0">
                <h3>测试结果</h3>
                <div class="results-list">
                    <div 
                        v-for="(result, index) in testResults" 
                        :key="index"
                        class="result-item"
                        :class="{ 'success': result.success, 'error': !result.success }"
                    >
                        <div class="result-header">
                            <span class="payment-name">{{ result.paymentMethod }}</span>
                            <span class="result-status">{{ result.success ? '成功' : '失败' }}</span>
                        </div>
                        <div class="result-details">
                            <p v-if="result.success">
                                支付ID: {{ result.paymentId }}<br>
                                处理时间: {{ result.timestamp }}
                            </p>
                            <p v-else class="error-message">
                                错误: {{ result.error }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="test-actions">
                <button @click="runAllTests" class="run-all-button" :disabled="testing">
                    {{ testing ? '测试中...' : '运行所有测试' }}
                </button>
                <button @click="clearResults" class="clear-button">清除结果</button>
                <button @click="goBack" class="back-button">返回确认页面</button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { PaymentFactory } from '@utils/payment';

const router = useRouter();

const testOrderNumber = ref('TEST_' + Date.now());
const testAmount = ref(10.00);
const testing = ref(false);
const testResults = ref([]);

const paymentMethods = ref([
    {
        id: 'paypal',
        name: 'PayPal',
        icon: '/src/assets/paypal.png',
        description: '全球领先的在线支付平台'
    },
    {
        id: 'stripe',
        name: 'Stripe',
        icon: '/src/assets/<EMAIL>',
        description: '专业的信用卡支付处理'
    },
    {
        id: 'bitpay',
        name: 'BitPay',
        icon: '/src/assets/<EMAIL>',
        description: '比特币和加密货币支付'
    },
    {
        id: 'wepay',
        name: '微信支付',
        icon: '/src/assets/<EMAIL>',
        description: '中国领先的移动支付'
    }
]);

// 测试单个支付方式
const testPayment = async (paymentMethod) => {
    try {
        testing.value = true;
        ElMessage.info(`开始测试 ${paymentMethod} 支付...`);
        
        const payment = PaymentFactory.createPayment(paymentMethod);
        const result = await payment.createPayment(testAmount.value, testOrderNumber.value);
        
        if (result.success) {
            testResults.value.unshift({
                paymentMethod: getPaymentName(paymentMethod),
                success: true,
                paymentId: result.paymentIntentId || result.orderId || result.invoiceId || result.prepayId,
                timestamp: new Date().toLocaleString()
            });
            ElMessage.success(`${paymentMethod} 支付测试成功！`);
        } else if (result.cancelled) {
            ElMessage.info(`${paymentMethod} 支付已取消`);
        } else {
            throw new Error('支付失败');
        }
    } catch (error) {
        console.error(`${paymentMethod} 支付测试失败:`, error);
        testResults.value.unshift({
            paymentMethod: getPaymentName(paymentMethod),
            success: false,
            error: error.message,
            timestamp: new Date().toLocaleString()
        });
        ElMessage.error(`${paymentMethod} 支付测试失败: ${error.message}`);
    } finally {
        testing.value = false;
    }
};

// 运行所有测试
const runAllTests = async () => {
    testing.value = true;
    ElMessage.info('开始运行所有支付方式测试...');
    
    for (const payment of paymentMethods.value) {
        await new Promise(resolve => setTimeout(resolve, 1000)); // 间隔1秒
        await testPayment(payment.id);
    }
    
    testing.value = false;
    ElMessage.success('所有支付方式测试完成！');
};

// 清除测试结果
const clearResults = () => {
    testResults.value = [];
    ElMessage.info('测试结果已清除');
};

// 获取支付方式名称
const getPaymentName = (id) => {
    const payment = paymentMethods.value.find(p => p.id === id);
    return payment ? payment.name : id;
};

// 返回确认页面
const goBack = () => {
    router.push('/confirm?step=2');
};

onMounted(() => {
    ElMessage.info('支付测试页面已加载，您可以测试各种支付方式');
});
</script>

<style lang="scss" scoped>
.payment-test {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem;

    .payment-test-container {
        max-width: 120rem;
        margin: 0 auto;
        background: white;
        border-radius: 1.6rem;
        padding: 3rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);

        h2 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 3rem;
            font-size: 2.8rem;
        }

        .test-info {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 3rem;
            text-align: center;

            p {
                margin: 0.5rem 0;
                font-size: 1.6rem;
                color: #495057;
            }
        }

        .payment-methods {
            margin-bottom: 3rem;

            h3 {
                color: #2c3e50;
                margin-bottom: 2rem;
                font-size: 2.2rem;
            }

            .payment-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
                gap: 2rem;

                .payment-method-card {
                    background: white;
                    border: 2px solid #e9ecef;
                    border-radius: 1rem;
                    padding: 2rem;
                    text-align: center;
                    cursor: pointer;
                    transition: all 0.3s ease;

                    &:hover {
                        border-color: #6e4aeb;
                        transform: translateY(-5px);
                        box-shadow: 0 10px 20px rgba(110, 74, 235, 0.2);
                    }

                    .payment-icon {
                        width: 6rem;
                        height: 4rem;
                        object-fit: contain;
                        margin-bottom: 1rem;
                    }

                    h4 {
                        color: #2c3e50;
                        margin: 1rem 0;
                        font-size: 1.8rem;
                    }

                    p {
                        color: #6c757d;
                        font-size: 1.4rem;
                        margin-bottom: 1.5rem;
                    }

                    .test-button {
                        background: #6e4aeb;
                        color: white;
                        border: none;
                        padding: 1rem 2rem;
                        border-radius: 0.5rem;
                        font-size: 1.4rem;
                        cursor: pointer;
                        transition: background 0.3s ease;

                        &:hover {
                            background: #5a3cd0;
                        }
                    }
                }
            }
        }

        .test-results {
            margin-bottom: 3rem;

            h3 {
                color: #2c3e50;
                margin-bottom: 2rem;
                font-size: 2.2rem;
            }

            .results-list {
                .result-item {
                    background: #f8f9fa;
                    border-left: 4px solid #6c757d;
                    padding: 1.5rem;
                    margin-bottom: 1rem;
                    border-radius: 0.5rem;

                    &.success {
                        border-left-color: #28a745;
                        background: #d4edda;
                    }

                    &.error {
                        border-left-color: #dc3545;
                        background: #f8d7da;
                    }

                    .result-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 1rem;

                        .payment-name {
                            font-weight: bold;
                            font-size: 1.6rem;
                        }

                        .result-status {
                            padding: 0.5rem 1rem;
                            border-radius: 0.3rem;
                            font-size: 1.2rem;
                            font-weight: bold;
                        }
                    }

                    &.success .result-status {
                        background: #28a745;
                        color: white;
                    }

                    &.error .result-status {
                        background: #dc3545;
                        color: white;
                    }

                    .result-details {
                        font-size: 1.4rem;
                        color: #495057;

                        .error-message {
                            color: #721c24;
                        }
                    }
                }
            }
        }

        .test-actions {
            display: flex;
            gap: 1.5rem;
            justify-content: center;
            flex-wrap: wrap;

            button {
                padding: 1.2rem 2.4rem;
                border: none;
                border-radius: 0.8rem;
                font-size: 1.6rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;

                &.run-all-button {
                    background: #28a745;
                    color: white;

                    &:hover:not(:disabled) {
                        background: #218838;
                    }

                    &:disabled {
                        background: #6c757d;
                        cursor: not-allowed;
                    }
                }

                &.clear-button {
                    background: #ffc107;
                    color: #212529;

                    &:hover {
                        background: #e0a800;
                    }
                }

                &.back-button {
                    background: #6c757d;
                    color: white;

                    &:hover {
                        background: #5a6268;
                    }
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .payment-test {
        padding: 1rem;

        .payment-test-container {
            padding: 2rem;

            .payment-grid {
                grid-template-columns: 1fr;
            }

            .test-actions {
                flex-direction: column;

                button {
                    width: 100%;
                }
            }
        }
    }
}
</style>
