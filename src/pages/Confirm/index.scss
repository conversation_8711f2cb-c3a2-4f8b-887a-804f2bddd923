.cart_view {
  padding: 2.9rem 30.1rem 15.5rem 30.1rem;
  background: #f6f6f6;
  box-sizing: border-box;
  &_title {
    height: 1.9rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 1.4rem;
    color: #70707b;
    margin-bottom: 2.9rem;
  }
  &_step {
    height: 9.7rem;
    background: #ffffff;
    border-radius: 1.9rem;
    margin-bottom: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    &_item {
      font-family: Robot<PERSON>, Roboto;
      font-weight: 400;
      font-size: 1.6rem;
      color: rgba(0, 0, 0, 0.45);
      display: flex;
      align-items: center;
      &_dot {
        width: 3.1rem;
        height: 3.1rem;
        background: #6e4aeb;
        border-radius: 3.1rem;
        border: 0.1rem solid #6e4aeb;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: <PERSON><PERSON>, <PERSON><PERSON>;
        font-weight: 400;
        font-size: 1.6rem;
        color: #ffffff;
      }
      &_dot.disable {
        color: rgba(0, 0, 0, 0.25);
        background: #ffffff;
        border-color: rgba(0, 0, 0, 0.25);
      }
      &_img {
        width: 3.1rem;
        height: 3.1rem;
        margin-right: 0.8rem;
      }
    }
    &_item.active {
      color: rgba(0, 0, 0, 0.85);
    }
    &_line {
      margin: 0 0.16rem;
      width: 27.9rem;
      height: 0px;
      border: 0.1rem solid #f0f0f0;
      margin: 0 0.4rem;
    }
    &_line.active {
      background: #6e4aeb;
    }
  }
  &_box {
    display: flex;
    justify-content: space-between;
    &_cartList {
      padding: 2.9rem 2.3rem 6.2rem 2.3rem;
      box-sizing: border-box;
      width: 89.2rem;
      background: #ffffff;
      border-radius: 1.9rem;
    }
    &_container {
      padding: 2.9rem 2.3rem 6.2rem 2.3rem;
      box-sizing: border-box;
      width: 89.2rem;
      background: #ffffff;
      border-radius: 1.9rem;
    }
    &_title {
      height: 2.7rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.9rem;
      color: #242426;
      margin-bottom: 2.9rem;
    }
    &_titles {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 2.7rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.9rem;
      color: #242426;
      margin-bottom: 2.9rem;
      &_num {
        width: 1.9rem;
        height: 1.9rem;
        background: #ff4d4f;
        border-radius: 9.7rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Roboto Mono, Roboto Mono;
        font-weight: 400;
        font-size: 1.2rem;
        color: #ffffff;
      }
    }
    &_line {
      width: 36.8rem;
      height: 0px;
      border: 0.1rem solid #e7e7e7;
      margin-bottom: 2.9rem;
    }
    &_info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.6rem;
      color: #70707b;
      line-height: 2.7rem;
    }
    &_lines {
      width: 36.8rem;
      height: 0px;
      border: 0.1rem solid #e7e7e7;
      margin-bottom: 1.3rem;
      margin-top: 2.3rem;
    }
    &_all {
      height: 2.7rem;
      font-family: PingFang SC, PingFang SC;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.6rem;
      color: #70707b;
      margin-bottom: 2.3rem;
      span {
        font-weight: 600;
        font-size: 1.9rem;
        color: #c33232;
      }
    }
    &_btn {
      width: 36.8rem;
      height: 4.7rem;
      background: #6e4aeb;
      border-radius: 3.1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 1.7rem;
      color: #ffffff;
    }
    &_add {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 4.7rem;
      background: #f6f6f6;
      border-radius: 1.2rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.6rem;
      color: #70707b;
      margin-bottom: 5rem;
      cursor: pointer;
      &_img {
        height: 2.3rem;
        width: 2.3rem;
        margin-right: 1.2rem;
      }
    }
    &_address {
      padding: 2.3rem 10rem 2.3rem 2.3rem;
      box-sizing: border-box;
      background: #ffffff;
      border-radius: 1.2rem;
      border: 0.1rem solid #6e4aeb;
      position: relative;
      margin-bottom: 1.2rem;
      &_user {
        margin-bottom: 0.8rem;
        display: flex;
        align-items: center;
        height: 2.3rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 1.6rem;
        color: #6e4aeb;
        span {
          padding: 0 0.8rem;
          height: 2.3rem;
          line-height: 2.3rem;
          background: #f3effe;
          border-radius: 0.4rem;
          font-weight: 400;
          font-size: 1.4rem;
        }
      }
      &_detail {
        line-height: 2.3rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.6rem;
        color: #70707b;
      }
      &_action {
        display: flex;
        align-items: center;
        position: absolute;
        right: 2.3rem;
        top: 50%;
        transform: translateY(-50%);
        &_icon {
          width: 2rem;
          height: 2rem;
          margin-left: 2rem;
          cursor: pointer;
        }
      }
    }
    &_paymentType {
      margin-bottom: 5rem;
      display: flex;
      align-items: center;
      gap: 1.6rem;
      &_item {
        width: 11.6rem;
        height: 7.8rem;
        border: 0.1rem solid #e7e7e7;
        border-radius: 1.2rem;
        box-sizing: border-box;
        cursor: pointer;
        img {
          width: 100%;
          height: 100%;
        }
      }
      &_item.active {
        border-color: #6e4aeb;
      }
    }
    &_deliver {
      padding: 0 2.1rem 0 1.6rem;
      box-sizing: border-box;
      height: 7.8rem;
      border-radius: 1.2rem;
      border: 0.1rem solid #6e4aeb;
      display: flex;
      align-items: center;
      width: max-content;
      margin-bottom: 5rem;
      &_icon {
        width: 3.9rem;
        height: 3.9rem;
      }
      &_main {
        padding-left: 1.2rem;
        &_tip1 {
          height: 2.3rem;
          line-height: 2.3rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: 600;
          font-size: 1.6rem;
          color: #6e4aeb;
          margin-bottom: 0.3rem;
        }
        &_tip2 {
          height: 2.3rem;
          line-height: 2.3rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 1.2rem;
          color: #70707b;
        }
      }
    }
    &_textarea {
      width: 100%;
      border: none;
      height: 11.4rem;
      background: #f3f4f8;
      border-radius: 1.2rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.6rem;
      color: #323232;
      line-height: 2.3rem;
      padding: 1.6rem;
      box-sizing: border-box;
      outline: none;
      resize: none;
    }
    &_textarea::placeholder {
      color: #70707b;
    }
    &_thead {
      width: 84.6rem;
      height: 3.5rem;
      background: #f6f6f6;
      display: flex;
      align-items: center;
      &_th {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.4rem;
        color: #70707b;
        .check {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 1.4rem !important;
          color: #70707b !important;
        }
      }
    }
    &_tr {
      padding: 2.3rem 0;
      border-bottom: 0.1rem solid #e7e7e7;
      display: flex;
      align-items: center;
      &_td {
        display: flex;
        align-items: center;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 1.6rem;
        color: #242426;
        img {
          width: 9.7rem;
          height: 9.7rem;
          margin-right: 1.6rem;
        }
        &_action {
          color: #6e4aeb;
          font-size: 1.4rem;
        }
      }
    }
    &_list {
      padding: 2.9rem 2.3rem 2.3rem 2.3rem;
      box-sizing: border-box;
      width: 41.5rem;
      background: #ffffff;
      border-radius: 1.9rem;
      margin-left: 1.3rem;
      &_item {
        padding: 2.3rem 1.3rem;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 0.1rem solid #e7e7e7;
        &_img {
          width: 9.7rem;
          height: 9.7rem;
        }
        &_main {
          flex: 1;
          padding-left: 1.6rem;
          box-sizing: border-box;
          &_name {
            height: 2.1rem;
            line-height: 2.1rem;
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 1.6rem;
            color: #242426;
            margin-bottom: 1.8rem;
          }
          &_info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 1.9rem;
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 1.4rem;
            color: #242426;
            span {
              color: #70707b;
            }
          }
        }
      }
    }
  }
  &_pay {
    padding: 5.8rem 26.2rem 8.8rem 26.2rem;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 1.9rem;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    &_img {
      width: 10.6rem;
      height: 10.1rem;
    }
    &_main {
      width: 65.9rem;
      &_title {
        height: 2.3rem;
        line-height: 100%;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 2.3rem;
        color: #242426;
        margin-bottom: 3.1rem;
      }
      &_info {
        height: 18.5rem;
        background: #f6f6f6;
        border-radius: 1.2rem;
        padding: 2.3rem;
        box-sizing: border-box;
        margin-bottom: 3.9rem;
        &_item {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 1.6rem;
          color: #70707b;
          display: flex;
          align-items: center;
          span {
            height: 3.1rem;
          }
          span.red {
            height: 2.7rem;
            font-weight: 600;
            font-size: 1.7rem;
            color: #c33232;
          }
          img {
            width: 11.6rem;
            height: 5.8rem;
          }
        }
      }
      &_action {
        display: flex;
        align-items: center;
        &_item {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 27.2rem;
          height: 4.7rem;
          background: #6e4aeb;
          border-radius: 3.1rem;
          margin-right: 1.6rem;
          color: #ffffff;
          font-family: PingFang SC, PingFang SC;
          font-weight: 600;
          font-size: 1.7rem;
          cursor: pointer;
          img {
            width: 1.9rem;
            height: 1.9rem;
            margin-left: 1.6rem;
          }
        }
        &_item:last-child {
          font-weight: 400;
          color: #242426;
          background: #f6f6f6;
          box-sizing: border-box;
          border: 0.1rem solid #e7e7e7;
        }
      }
    }
  }
}
.mb8 {
  margin-bottom: 0.8rem;
}
.mb15 {
  margin-bottom: 1.4rem;
}
.w117 {
  width: 11.7rem;
  padding-left: 2.3rem;
  box-sizing: border-box;
}
.w377 {
  width: 33.7rem;
}
.w114 {
  width: 11.4rem;
}
.w147 {
  width: 14.7rem;
}
.w1 {
  flex: 1;
}
::v-deep(.check .el-checkbox__label) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 1.4rem !important;
  color: #70707b !important;
}
