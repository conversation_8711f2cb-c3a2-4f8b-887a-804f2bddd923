// 支付功能测试工具
import { PaymentFactory } from './payment';
import { ElMessage } from 'element-plus';

/**
 * 测试PayPal支付
 */
export const testPayPalPayment = async () => {
  try {
    console.log('开始测试PayPal支付...');
    
    const paypal = PaymentFactory.createPayment('paypal', {
      clientId: 'test_client_id',
      environment: 'sandbox'
    });
    
    // 创建测试容器
    const testContainer = document.createElement('div');
    testContainer.id = 'paypal-test-container';
    testContainer.innerHTML = '<div id="paypal-button-container"></div>';
    document.body.appendChild(testContainer);
    
    const result = await paypal.createPayment(10.00, 'TEST_ORDER_001');
    console.log('PayPal测试结果:', result);
    
    // 清理测试容器
    testContainer.remove();
    
    return { success: true, message: 'PayPal测试完成' };
  } catch (error) {
    console.error('PayPal测试失败:', error);
    return { success: false, error: error.message };
  }
};

/**
 * 测试Stripe支付
 */
export const testStripePayment = async () => {
  try {
    console.log('开始测试Stripe支付...');
    
    const stripe = PaymentFactory.createPayment('stripe', {
      publishableKey: 'pk_test_example'
    });
    
    // 模拟支付测试
    console.log('Stripe支付对象创建成功');
    
    return { success: true, message: 'Stripe测试完成' };
  } catch (error) {
    console.error('Stripe测试失败:', error);
    return { success: false, error: error.message };
  }
};

/**
 * 测试BitPay支付
 */
export const testBitPayPayment = async () => {
  try {
    console.log('开始测试BitPay支付...');
    
    const bitpay = PaymentFactory.createPayment('bitpay', {
      environment: 'test'
    });
    
    console.log('BitPay支付对象创建成功');
    
    return { success: true, message: 'BitPay测试完成' };
  } catch (error) {
    console.error('BitPay测试失败:', error);
    return { success: false, error: error.message };
  }
};

/**
 * 测试微信支付
 */
export const testWePayPayment = async () => {
  try {
    console.log('开始测试微信支付...');
    
    const wepay = PaymentFactory.createPayment('wepay', {
      appId: 'test_app_id',
      environment: 'sandbox'
    });
    
    console.log('微信支付对象创建成功');
    
    return { success: true, message: '微信支付测试完成' };
  } catch (error) {
    console.error('微信支付测试失败:', error);
    return { success: false, error: error.message };
  }
};

/**
 * 运行所有支付测试
 */
export const runAllPaymentTests = async () => {
  console.log('开始运行所有支付测试...');
  
  const results = {
    paypal: await testPayPalPayment(),
    stripe: await testStripePayment(),
    bitpay: await testBitPayPayment(),
    wepay: await testWePayPayment()
  };
  
  console.log('所有测试结果:', results);
  
  const successCount = Object.values(results).filter(r => r.success).length;
  const totalCount = Object.keys(results).length;
  
  if (successCount === totalCount) {
    ElMessage.success(`所有支付方式测试通过 (${successCount}/${totalCount})`);
  } else {
    ElMessage.warning(`部分支付方式测试失败 (${successCount}/${totalCount})`);
  }
  
  return results;
};

/**
 * 验证支付配置
 */
export const validatePaymentConfig = () => {
  const config = {
    paypal: {
      clientId: import.meta.env.VITE_PAYPAL_CLIENT_ID,
      environment: import.meta.env.VITE_PAYPAL_ENVIRONMENT
    },
    stripe: {
      publishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY
    },
    bitpay: {
      environment: import.meta.env.VITE_BITPAY_ENVIRONMENT,
      apiUrl: import.meta.env.VITE_BITPAY_API_URL
    },
    wepay: {
      appId: import.meta.env.VITE_WECHAT_APP_ID,
      environment: import.meta.env.VITE_WECHAT_ENVIRONMENT
    }
  };
  
  const issues = [];
  
  // 检查PayPal配置
  if (!config.paypal.clientId) {
    issues.push('PayPal Client ID 未配置');
  }
  
  // 检查Stripe配置
  if (!config.stripe.publishableKey) {
    issues.push('Stripe Publishable Key 未配置');
  }
  
  // 检查微信支付配置
  if (!config.wepay.appId) {
    issues.push('微信支付 App ID 未配置');
  }
  
  if (issues.length > 0) {
    console.warn('支付配置问题:', issues);
    ElMessage.warning(`支付配置不完整: ${issues.join(', ')}`);
    return { valid: false, issues };
  }
  
  console.log('支付配置验证通过');
  ElMessage.success('支付配置验证通过');
  return { valid: true, config };
};
