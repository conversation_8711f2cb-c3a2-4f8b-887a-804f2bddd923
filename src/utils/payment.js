import { ElMessage, ElLoading } from 'element-plus';

/**
 * PayPal支付处理
 */
export class PayPalPayment {
  constructor(config = {}) {
    this.clientId = config.clientId;
    this.environment = config.environment || 'sandbox'; // sandbox 或 production
    this.currency = config.currency || 'USD';
  }

  // 初始化PayPal SDK
  async initSDK() {
    return new Promise((resolve, reject) => {
      if (window.paypal) {
        resolve(window.paypal);
        return;
      }

      const script = document.createElement('script');
      script.src = `https://www.paypal.com/sdk/js?client-id=${this.clientId}&currency=${this.currency}`;
      script.onload = () => resolve(window.paypal);
      script.onerror = () => reject(new Error('PayPal SDK加载失败'));
      document.head.appendChild(script);
    });
  }

  // 创建支付
  async createPayment(
    amount,
    orderNumber,
    containerId = 'paypal-button-container'
  ) {
    try {
      console.log('创建支付', amount, orderNumber);
      const paypal = await this.initSDK();

      return paypal
        .Buttons({
          createOrder: (data, actions) => {
            return actions.order.create({
              purchase_units: [
                {
                  amount: {
                    value: amount,
                    currency_code: this.currency,
                  },
                  custom_id: orderNumber,
                },
              ],
            });
          },
          onApprove: async (data, actions) => {
            const order = await actions.order.capture();
            return {
              success: true,
              orderId: data.orderID,
              paymentId: order.id,
              details: order,
            };
          },
          onError: (err) => {
            console.error('PayPal支付错误:', err);
            ElMessage.error('PayPal支付失败');
            return { success: false, error: err };
          },
        })
        .render(`#${containerId}`);
    } catch (error) {
      console.error('PayPal初始化失败:', error);
      ElMessage.error('PayPal初始化失败');
      throw error;
    }
  }
}

/**
 * Stripe支付处理
 */
export class StripePayment {
  constructor(config = {}) {
    console.log('StripePayment config:', config);
    this.publishableKey = config.publishableKey;
    this.client_secret = config.secret_key;
    this.stripe = null;
  }

  // 初始化Stripe SDK
  async initSDK() {
    return new Promise((resolve, reject) => {
      if (window.Stripe) {
        this.stripe = window.Stripe(this.publishableKey);
        resolve(this.stripe);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://js.stripe.com/v3/';
      script.onload = () => {
        this.stripe = window.Stripe(this.publishableKey);
        resolve(this.stripe);
      };
      script.onerror = () => reject(new Error('Stripe SDK加载失败'));
      document.head.appendChild(script);
    });
  }

  // 创建支付
  async createPayment(amount, orderNumber, currency = 'usd') {
    try {
      await this.initSDK();

      // 创建支付表单界面
      const paymentForm = this.createPaymentForm(amount, orderNumber, currency);
      document.body.appendChild(paymentForm);

      return new Promise((resolve, reject) => {
        // 设置全局处理函数
        window.handleStripePayment = async (cardData) => {
          try {
            const result = await this.processStripePayment(
              cardData,
              amount,
              orderNumber,
              currency
            );
            paymentForm.remove();
            resolve(result);
          } catch (error) {
            paymentForm.remove();
            reject(error);
          }
        };

        window.cancelStripePayment = () => {
          paymentForm.remove();
          resolve({ success: false, cancelled: true });
        };
      });
    } catch (error) {
      console.error('Stripe支付处理失败:', error);
      ElMessage.error('Stripe支付处理失败');
      throw error;
    }
  }

  // 创建支付表单
  createPaymentForm(amount, orderNumber, currency) {
    const overlay = document.createElement('div');
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
    `;

    overlay.innerHTML = `
      <div style="
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        max-width: 400px;
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
      ">
        <h3 style="margin-top: 0; text-align: center; color: #333;">Stripe 支付</h3>

        <form id="stripe-payment-form">
          <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">卡号</label>
            <input type="text" id="card-number" placeholder="4242 4242 4242 4242" maxlength="19"
                   style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 6px; box-sizing: border-box; font-size: 16px;">
          </div>

          <div style="display: flex; gap: 10px; margin-bottom: 15px;">
            <div style="flex: 1;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">过期日期</label>
              <input type="text" id="card-expiry" placeholder="MM/YY" maxlength="5"
                     style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 6px; box-sizing: border-box; font-size: 16px;">
            </div>
            <div style="flex: 1;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">CVC</label>
              <input type="text" id="card-cvc" placeholder="123" maxlength="4"
                     style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 6px; box-sizing: border-box; font-size: 16px;">
            </div>
          </div>

          <div style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">持卡人姓名</label>
            <input type="text" id="card-name" placeholder="John Doe"
                   style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 6px; box-sizing: border-box; font-size: 16px;">
          </div>

          <div style="display: flex; gap: 10px;">
            <button type="submit" id="pay-button" style="
              flex: 1;
              background: #6772e5;
              color: white;
              border: none;
              padding: 14px;
              border-radius: 6px;
              font-size: 16px;
              font-weight: bold;
              cursor: pointer;
              transition: background-color 0.2s;
            ">支付 $${amount}</button>

            <button type="button" onclick="cancelStripePayment()" style="
              flex: 1;
              background: #6c757d;
              color: white;
              border: none;
              padding: 14px;
              border-radius: 6px;
              font-size: 16px;
              font-weight: bold;
              cursor: pointer;
              transition: background-color 0.2s;
            ">取消</button>
          </div>
        </form>

       
      </div>
    `;

    // 添加卡号格式化
    const cardNumberInput = overlay.querySelector('#card-number');
    cardNumberInput.addEventListener('input', (e) => {
      let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
      let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
      e.target.value = formattedValue;
    });

    // 添加过期日期格式化
    const expiryInput = overlay.querySelector('#card-expiry');
    expiryInput.addEventListener('input', (e) => {
      let value = e.target.value.replace(/\D/g, '');
      if (value.length >= 2) {
        value = value.substring(0, 2) + '/' + value.substring(2, 4);
      }
      e.target.value = value;
    });

    // 添加CVC数字限制
    const cvcInput = overlay.querySelector('#card-cvc');
    cvcInput.addEventListener('input', (e) => {
      e.target.value = e.target.value.replace(/[^0-9]/g, '');
    });

    // 添加表单提交事件
    const form = overlay.querySelector('#stripe-payment-form');
    form.addEventListener('submit', (e) => {
      e.preventDefault();

      const cardData = {
        number: cardNumberInput.value.replace(/\s/g, ''),
        expiry: expiryInput.value,
        cvc: cvcInput.value,
        name: overlay.querySelector('#card-name').value,
      };

      // 禁用按钮防止重复提交
      const payButton = overlay.querySelector('#pay-button');
      payButton.disabled = true;
      payButton.textContent = '处理中...';

      window.handleStripePayment(cardData);
    });

    return overlay;
  }

  // 处理Stripe支付
  async processStripePayment(cardData, amount, orderNumber, currency) {
    try {
      // 验证卡片数据
      if (
        !cardData.number ||
        !cardData.expiry ||
        !cardData.cvc ||
        !cardData.name
      ) {
        throw new Error('请填写完整的卡片信息');
      }

      if (cardData.number.length < 13) {
        throw new Error('请输入有效的卡号');
      }

      if (cardData.cvc.length < 3) {
        throw new Error('请输入有效的CVC');
      }

      ElMessage.info('正在处理Stripe支付...');

      // 模拟支付处理延迟
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // 模拟支付成功（在实际项目中，这里应该调用后端API创建PaymentIntent）
      const mockPaymentIntent = {
        id: 'pi_' + Math.random().toString(36).substr(2, 9),
        status: 'succeeded',
        amount: Math.round(amount * 100),
        currency: currency,
        payment_method: {
          card: {
            last4: cardData.number.slice(-4),
            brand: 'visa',
          },
        },
      };

      ElMessage.success('Stripe支付成功！');

      return {
        success: true,
        paymentIntentId: mockPaymentIntent.id,
        details: mockPaymentIntent,
      };
    } catch (error) {
      console.error('Stripe支付处理失败:', error);
      ElMessage.error('Stripe支付失败: ' + error.message);
      throw error;
    }
  }
}

/**
 * BitPay支付处理
 */
export class BitPayPayment {
  constructor(config = {}) {
    this.apiUrl = config.apiUrl || 'https://bitpay.com/api';
    this.environment = config.environment || 'test'; // test 或 prod
  }

  // 创建支付
  async createPayment(amount, orderNumber, currency = 'USD') {
    try {
      const loading = ElLoading.service({
        lock: true,
        text: '正在创建BitPay支付...',
        background: 'rgba(0, 0, 0, 0.7)',
      });

      // 调用后端API创建BitPay发票
      const response = await fetch('/api/bitpay/create-invoice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          price: amount,
          currency,
          orderNumber,
          redirectURL: window.location.origin + '/payment/success',
          notificationURL: window.location.origin + '/api/bitpay/webhook',
        }),
      });

      const invoice = await response.json();
      loading.close();

      if (invoice.url) {
        // 跳转到BitPay支付页面
        window.open(invoice.url, '_blank');
        return {
          success: true,
          invoiceId: invoice.id,
          paymentUrl: invoice.url,
          details: invoice,
        };
      } else {
        throw new Error('创建BitPay发票失败');
      }
    } catch (error) {
      console.error('BitPay支付创建失败:', error);
      ElMessage.error('BitPay支付创建失败');
      throw error;
    }
  }
}

/**
 * 微信支付处理
 */
export class WePayPayment {
  constructor(config = {}) {
    this.appId = config.appId;
    this.environment = config.environment || 'sandbox';
  }

  // 创建支付
  async createPayment(amount, orderNumber) {
    try {
      const loading = ElLoading.service({
        lock: true,
        text: '正在创建微信支付...',
        background: 'rgba(0, 0, 0, 0.7)',
      });

      // 调用后端API创建微信支付订单
      const response = await fetch('/api/wepay/create-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: Math.round(amount * 100), // 微信支付使用分为单位
          orderNumber,
          description: `订单支付 - ${orderNumber}`,
        }),
      });

      const paymentData = await response.json();
      loading.close();

      if (paymentData.code_url) {
        // 显示二维码供用户扫描
        this.showQRCode(paymentData.code_url, orderNumber);
        return {
          success: true,
          prepayId: paymentData.prepay_id,
          codeUrl: paymentData.code_url,
          details: paymentData,
        };
      } else {
        throw new Error('创建微信支付订单失败');
      }
    } catch (error) {
      console.error('微信支付创建失败:', error);
      ElMessage.error('微信支付创建失败');
      throw error;
    }
  }

  // 显示二维码
  showQRCode(codeUrl, orderNumber) {
    // 这里可以使用qrcode库生成二维码
    // 或者直接显示二维码URL让用户扫描
    const qrWindow = window.open('', '_blank', 'width=400,height=500');
    qrWindow.document.write(`
      <html>
        <head><title>微信支付</title></head>
        <body style="text-align: center; padding: 20px;">
          <h3>请使用微信扫描二维码支付</h3>
          <div id="qrcode"></div>
          <p>订单号: ${orderNumber}</p>
          <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
          <script>
            QRCode.toCanvas(document.getElementById('qrcode'), '${codeUrl}', function (error) {
              if (error) console.error(error);
            });
          </script>
        </body>
      </html>
    `);
  }
}

/**
 * 支付工厂类
 */
export class PaymentFactory {
  static createPayment(method, config = {}) {
    switch (method) {
      case 'paypal':
        return new PayPalPayment(config);
      case 'stripe':
        return new StripePayment(config);
      case 'bitpay':
        return new BitPayPayment(config);
      case 'wepay':
        return new WePayPayment(config);
      default:
        throw new Error(`不支持的支付方式: ${method}`);
    }
  }
}
