import { get, post } from '@utils/request';

/**
 * 用户登录
 * @param {string} email - 邮箱
 * @param {string} password - 密码
 * @returns {Promise}
 */
export const login = (data) => {
  return post('/login', data);
};

/**
 * 用户注册
 * @param {string} email - 邮箱
 * @param {string} password - 密码
 * @returns {Promise}
 */
export const register = (data) => {
  return post('/register', data);
};

/**
 * 用户登出
 * @returns {Promise}
 */
export const logout = () => {
  return get('/logout');
};
