# API 最佳实践指南

## 🎯 推荐的开发模式

### 1. 使用 Composition API 封装业务逻辑

创建自定义 hooks 来封装 API 调用和状态管理：

```javascript
// composables/useAuth.js
import { ref } from 'vue';
import { login, register, logout } from '@api/auth';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';

export const useAuth = () => {
  const loading = ref(false);
  const user = ref(null);
  const router = useRouter();

  const handleLogin = async (credentials) => {
    loading.value = true;
    try {
      const result = await login(credentials);
      user.value = result.user;
      ElMessage.success('登录成功');
      router.push('/');
      return result;
    } catch (error) {
      ElMessage.error('登录失败');
      throw error;
    } finally {
      loading.value = false;
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      user.value = null;
      ElMessage.success('退出成功');
      router.push('/login');
    } catch (error) {
      ElMessage.error('退出失败');
    }
  };

  return {
    loading,
    user,
    handleLogin,
    handleLogout,
  };
};
```

### 2. 错误处理最佳实践

```javascript
// utils/errorHandler.js
import { ElMessage, ElNotification } from 'element-plus';

export const handleApiError = (error, customMessage) => {
  console.error('API Error:', error);

  // 根据错误类型显示不同提示
  if (error.response) {
    const { status, data } = error.response;
    switch (status) {
      case 401:
        ElMessage.error('登录已过期，请重新登录');
        // 跳转到登录页
        break;
      case 403:
        ElMessage.error('没有权限执行此操作');
        break;
      case 404:
        ElMessage.error('请求的资源不存在');
        break;
      case 422:
        // 表单验证错误
        if (data.errors) {
          Object.values(data.errors).forEach((msgs) => {
            msgs.forEach((msg) => ElMessage.error(msg));
          });
        }
        break;
      case 500:
        ElNotification.error({
          title: '服务器错误',
          message: '服务器暂时无法处理您的请求，请稍后重试',
        });
        break;
      default:
        ElMessage.error(customMessage || data.message || '请求失败');
    }
  } else if (error.request) {
    ElMessage.error('网络连接异常，请检查网络');
  } else {
    ElMessage.error(customMessage || '请求发生未知错误');
  }
};

// 在 API 调用中使用
import { handleApiError } from '@utils/errorHandler';

const loadData = async () => {
  try {
    const result = await getCartList();
    // 处理成功逻辑
  } catch (error) {
    handleApiError(error, '获取购物车数据失败');
  }
};
```

### 3. 加载状态管理

```javascript
// composables/useLoading.js
import { ref } from 'vue';

export const useLoading = () => {
  const loading = ref(false);
  const loadingText = ref('加载中...');

  const withLoading = async (asyncFn, text = '加载中...') => {
    loading.value = true;
    loadingText.value = text;
    try {
      return await asyncFn();
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    loadingText,
    withLoading,
  };
};

// 使用示例
const { loading, withLoading } = useLoading();

const loadCartData = () => {
  return withLoading(async () => {
    const result = await getCartList();
    cartList.value = result.carts;
  }, '正在加载购物车...');
};
```

### 4. 数据缓存策略

```javascript
// utils/apiCache.js
class ApiCache {
  constructor() {
    this.cache = new Map();
    this.expireTime = 5 * 60 * 1000; // 5分钟过期
  }

  set(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() - item.timestamp > this.expireTime) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  clear() {
    this.cache.clear();
  }
}

export const apiCache = new ApiCache();

// 带缓存的 API 调用
export const getCachedProductDetail = async (productId) => {
  const cacheKey = `product_${productId}`;
  const cached = apiCache.get(cacheKey);

  if (cached) {
    return cached;
  }

  const result = await getProductDetail(productId);
  apiCache.set(cacheKey, result);
  return result;
};
```

### 5. 批量操作优化

```javascript
// utils/batchOperations.js
export const createBatchProcessor = (batchSize = 10, delay = 100) => {
  let queue = [];
  let processing = false;

  const processBatch = async () => {
    if (processing || queue.length === 0) return;

    processing = true;
    const batch = queue.splice(0, batchSize);

    try {
      await Promise.all(batch.map((item) => item.fn()));
      batch.forEach((item) => item.resolve());
    } catch (error) {
      batch.forEach((item) => item.reject(error));
    }

    processing = false;

    if (queue.length > 0) {
      setTimeout(processBatch, delay);
    }
  };

  return (fn) => {
    return new Promise((resolve, reject) => {
      queue.push({ fn, resolve, reject });
      processBatch();
    });
  };
};

// 使用示例：批量删除购物车商品
const batchRemove = createBatchProcessor(5, 200);

const removeMultipleItems = async (cartIds) => {
  const promises = cartIds.map((cartId) =>
    batchRemove(() => removeCartItem(cartId))
  );

  try {
    await Promise.all(promises);
    ElMessage.success('批量删除成功');
  } catch (error) {
    ElMessage.error('批量删除失败');
  }
};
```

### 6. 实时数据同步

```javascript
// composables/useRealTimeCart.js
import { ref, onMounted, onUnmounted } from 'vue';
import { getMiniCart } from '@api/cart';

export const useRealTimeCart = () => {
  const cartCount = ref(0);
  const cartAmount = ref(0);
  let timer = null;

  const updateCartInfo = async () => {
    try {
      const result = await getMiniCart();
      cartCount.value = result.quantity || 0;
      cartAmount.value = result.amount || 0;
    } catch (error) {
      console.error('更新购物车信息失败:', error);
    }
  };

  const startPolling = (interval = 30000) => {
    updateCartInfo(); // 立即执行一次
    timer = setInterval(updateCartInfo, interval);
  };

  const stopPolling = () => {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
  };

  onMounted(() => startPolling());
  onUnmounted(() => stopPolling());

  return {
    cartCount,
    cartAmount,
    updateCartInfo,
    startPolling,
    stopPolling,
  };
};
```

### 7. 表单验证集成

```javascript
// composables/useFormValidation.js
import { reactive } from 'vue';

export const useFormValidation = (rules) => {
  const errors = reactive({});
  const isValid = ref(true);

  const validate = (data) => {
    Object.keys(errors).forEach((key) => delete errors[key]);
    isValid.value = true;

    Object.keys(rules).forEach((field) => {
      const fieldRules = rules[field];
      const value = data[field];

      for (const rule of fieldRules) {
        if (!rule.validator(value)) {
          errors[field] = rule.message;
          isValid.value = false;
          break;
        }
      }
    });

    return isValid.value;
  };

  return { errors, isValid, validate };
};

// 使用示例
const loginRules = {
  email: [
    { validator: (v) => !!v, message: '请输入邮箱' },
    { validator: (v) => /\S+@\S+\.\S+/.test(v), message: '邮箱格式不正确' },
  ],
  password: [
    { validator: (v) => !!v, message: '请输入密码' },
    { validator: (v) => v.length >= 6, message: '密码至少6位' },
  ],
};

const { errors, isValid, validate } = useFormValidation(loginRules);

const handleSubmit = async () => {
  if (!validate(formData)) {
    return;
  }

  try {
    await login(formData);
  } catch (error) {
    // 处理错误
  }
};
```

## 🚨 注意事项

1. **避免内存泄漏**：在组件销毁时清理定时器和事件监听
2. **合理使用缓存**：敏感数据不要缓存，用户相关数据注意清理
3. **错误边界**：重要的 API 调用都要有 try-catch
4. **用户体验**：长时间操作要显示加载状态
5. **网络优化**：避免重复请求，合理使用防抖和节流

## 📊 性能监控

```javascript
// utils/apiMonitor.js
export const apiMonitor = {
  requests: new Map(),

  start(url) {
    this.requests.set(url, Date.now());
  },

  end(url, success = true) {
    const startTime = this.requests.get(url);
    if (startTime) {
      const duration = Date.now() - startTime;
      console.log(
        `API ${url}: ${duration}ms (${success ? 'success' : 'failed'})`
      );
      this.requests.delete(url);
    }
  },
};

// 在 request.js 中集成监控
service.interceptors.request.use((config) => {
  apiMonitor.start(config.url);
  return config;
});

service.interceptors.response.use(
  (response) => {
    apiMonitor.end(response.config.url, true);
    return response;
  },
  (error) => {
    apiMonitor.end(error.config?.url, false);
    return Promise.reject(error);
  }
);
```
