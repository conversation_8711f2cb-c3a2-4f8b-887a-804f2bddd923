import { get, post, put, del } from '@utils/request';

/**
 * 获取购物车列表
 * @returns {Promise}
 */
export const getCartList = () => {
  return get('/carts');
};

/**
 * 获取迷你购物车信息
 * @returns {Promise}
 */
export const getMiniCart = () => {
  return get('/carts/mini');
};

/**
 * 添加商品到购物车
 * @param {Object} data - 商品信息
 * @param {boolean} data.buy_now - 是否现在购买
 * @param {number} data.quantity - 数量
 * @param {number} data.sku_id - SKU ID
 * @returns {Promise}
 */
export const addToCart = (data) => {
  return post('/carts', data);
};

/**
 * 更新购物车商品
 * @param {number} cartId - 购物车ID
 * @param {Object} data - 更新数据
 * @param {number} data.quantity - 数量
 * @param {number} data.sku_id - SKU ID
 * @returns {Promise}
 */
export const updateCart = (cartId, data) => {
  return put(`/carts/${cartId}`, data);
};

/**
 * 选中购物车商品
 * @param {Array} cartIds - 购物车商品ID数组
 * @returns {Promise}
 */
export const selectCartItems = (cartIds) => {
  return post('/carts/select', { cart_ids: cartIds });
};

/**
 * 取消选中购物车商品
 * @param {Array} cartIds - 购物车商品ID数组
 * @returns {Promise}
 */
export const unselectCartItems = (cartIds) => {
  return post('/carts/unselect', { cart_ids: cartIds });
};

/**
 * 删除购物车商品
 * @param {number} cartId - 购物车ID
 * @returns {Promise}
 */
export const removeCartItem = (cartId) => {
  return del(`/v1/carts/${cartId}`);
};
