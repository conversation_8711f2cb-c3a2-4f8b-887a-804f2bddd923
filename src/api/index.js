// 统一导出所有 API 接口
export * as authAPI from './auth';
export * as homeAPI from './home';
export * as productAPI from './product';
export * as cartAPI from './cart';
export * as checkoutAPI from './checkout';
export * as orderAPI from './order';
export * as accountAPI from './account';
export * as addressAPI from './address';
export * as wishlistAPI from './wishlist';

// 也可以单独导入使用
export { login, register, logout } from './auth';
export { getHomeInfo, getLatestProducts } from './home';
export { getProductDetail } from './product';
export {
  getCartList,
  getMiniCart,
  addToCart,
  updateCart,
  selectCartItems,
  unselectCartItems,
  removeCartItem,
} from './cart';
export {
  getCheckoutInfo,
  updateCheckout,
  confirmOrder,
  getCheckoutSuccess,
} from './checkout';
export {
  getOrderDetail,
  getOrderPayInfo,
  cancelOrder,
  completeOrder,
} from './order';
export {
  getAccountInfo,
  getUserInfo,
  updateUserInfo,
  getPasswordPage,
  updatePassword,
  getUserOrders,
  getUserOrderDetail,
  getRMAList,
  getRMADetail,
  createRMAPage,
  submitRMA,
} from './account';
export {
  getAddressList,
  getAddressDetail,
  createAddress,
  updateAddress,
  deleteAddress,
} from './address';
export { getWishlist, addToWishlist, removeFromWishlist } from './wishlist';
