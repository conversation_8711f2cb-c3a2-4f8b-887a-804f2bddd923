# 🚀 API 快速开始指南

## 1. 立即开始使用

### 在页面中导入 API

```javascript
// 推荐：按需导入
import { login, getCartList, addToCart } from '@api/index';

// 或者：按模块导入
import { authAPI, cartAPI } from '@api/index';
```

### 基本使用模式

```javascript
// 在 Vue 组件中使用
<script setup>
import { ref } from 'vue';
import { login } from '@api/auth';
import { ElMessage } from 'element-plus';

const loginForm = ref({
  email: '',
  password: ''
});

const handleLogin = async () => {
  try {
    const result = await login(loginForm.value);
    ElMessage.success('登录成功');
    // 处理成功逻辑
  } catch (error) {
    ElMessage.error('登录失败');
  }
};
</script>
```

## 2. 常用场景示例

### 🔐 用户认证

```javascript
import { login, register, logout } from '@api/auth';

// 登录
await login({ email: '<EMAIL>', password: '123456' });

// 注册
await register({ email: '<EMAIL>', password: '123456' });

// 登出
await logout();
```

### 🛒 购物车操作

```javascript
import { getCartList, addToCart, updateCart } from '@api/cart';

// 获取购物车
const cartData = await getCartList();

// 添加商品
await addToCart({ sku_id: 123, quantity: 1, buy_now: false });

// 更新数量
await updateCart(cartId, { quantity: 2 });
```

### 👤 用户信息

```javascript
import { getAccountInfo, updateUserInfo } from '@api/account';

// 获取用户信息
const userInfo = await getAccountInfo();

// 更新用户信息
await updateUserInfo({ name: '新名字', email: '<EMAIL>' });
```

### 📍 地址管理

```javascript
import { getAddressList, createAddress } from '@api/address';

// 获取地址列表
const addresses = await getAddressList();

// 创建新地址
await createAddress({
  name: '收货人',
  phone: '***********',
  country_id: 1,
  city: '北京市',
  address_1: '详细地址',
});
```

## 3. 与现有页面集成

### 更新登录页面

你的登录页面已经更新为使用新的 API 结构：

```javascript
// src/pages/Login/index.vue
import { login, register } from '@api/auth';

const doLogin = async () => {
  // 验证逻辑...

  try {
    const result = await login({
      email: login.email,
      password: login.password,
    });
    router.replace('/');
  } catch (error) {
    console.error('登录失败:', error);
  }
};
```

### 在其他页面中使用

```javascript
// 在任何页面组件中
import { getProductDetail } from '@api/product';
import { addToWishlist } from '@api/wishlist';

export default {
  async created() {
    // 页面初始化时加载数据
    await this.loadProductData();
  },

  methods: {
    async loadProductData() {
      try {
        const product = await getProductDetail(this.$route.params.id);
        this.product = product;
      } catch (error) {
        this.$message.error('加载商品详情失败');
      }
    },

    async addToWishlist() {
      try {
        await addToWishlist(this.product.id);
        this.$message.success('收藏成功');
      } catch (error) {
        this.$message.error('收藏失败');
      }
    },
  },
};
```

## 4. 错误处理

### 基本错误处理

```javascript
try {
  const result = await someAPI();
  // 处理成功结果
} catch (error) {
  // 处理错误
  console.error('API调用失败:', error);
  ElMessage.error('操作失败');
}
```

### 统一错误处理

```javascript
import { handleApiError } from '@utils/errorHandler';

try {
  const result = await someAPI();
} catch (error) {
  handleApiError(error, '自定义错误信息');
}
```

## 5. 加载状态管理

```javascript
import { ref } from 'vue';

const loading = ref(false);
const data = ref(null);

const loadData = async () => {
  loading.value = true;
  try {
    data.value = await getApiData();
  } catch (error) {
    ElMessage.error('加载失败');
  } finally {
    loading.value = false;
  }
};
```

## 6. 实际项目中的建议

### 项目结构建议

```
src/
├── api/                 # API 接口文件
│   ├── auth.js
│   ├── cart.js
│   └── index.js
├── composables/         # 可复用的组合式函数
│   ├── useAuth.js
│   ├── useCart.js
│   └── useLoading.js
├── utils/              # 工具函数
│   ├── errorHandler.js
│   └── apiCache.js
└── pages/              # 页面组件
    ├── Login/
    ├── Cart/
    └── Profile/
```

### 命名约定

- API 函数使用动词开头：`getXxx`, `createXxx`, `updateXxx`, `deleteXxx`
- 组合式函数使用 `useXxx` 格式
- 错误处理统一使用 try-catch
- 加载状态统一管理

## 7. 下一步

1. **查看完整文档**：阅读 `src/api/README.md` 了解所有可用接口
2. **查看最佳实践**：阅读 `src/api/best-practices.md` 学习高级用法
3. **开始开发**：在你的页面中导入需要的 API 函数开始使用

## 🎯 快速验证

运行以下命令验证 API 结构是否正确：

```bash
# 检查所有 API 文件是否存在
ls -la src/api/

# 检查导入是否正常（在浏览器控制台运行）
import { login } from '@api/auth';
console.log('API 导入成功！');
```

现在你可以开始在项目中使用这些模块化的 API 接口了！🎉
