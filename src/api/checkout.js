import { get, post, put } from '@utils/request';

/**
 * 获取结算页信息
 * @returns {Promise}
 */
export const GetCheckoutInfo = () => {
  return get('/checkout');
};

/**
 * 更新结算信息
 * @param {Object} data - 结算信息
 * @param {number} data.shipping_address_id - 配送地址ID
 * @param {string} data.shipping_method_code - 配送方式代码
 * @param {number} data.payment_address_id - 支付地址ID
 * @param {string} data.payment_method_code - 支付方式代码
 * @param {Object} data.guest_shipping_address - 游客配送地址
 * @param {Object} data.guest_payment_address - 游客支付地址
 * @returns {Promise}
 */
export const UpdateCheckout = (data) => {
  return put('/v1/checkout', data);
};

/**
 * 确认订单
 * @returns {Promise}
 */
export const ConfirmOrder = () => {
  return post('/v1/checkout/confirm');
};

/**
 * 结算成功页
 * @param {string} orderNumber - 订单号
 * @returns {Promise}
 */
export const GetCheckoutSuccess = (orderNumber) => {
  return get('/v1/checkout/success', { order_number: orderNumber });
};
