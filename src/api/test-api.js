// API 测试验证脚本
// 用于验证所有 API 模块是否正确导入和配置

console.log('🚀 开始验证 API 模块...');

// 测试导入 - 使用动态导入避免语法错误
const testImports = async () => {
  try {
    // 测试按需导入
    const indexModule = await import('./index.js');
    const { login, register, getCartList, addToCart, getAccountInfo } =
      indexModule;

    if (login && register && getCartList && addToCart && getAccountInfo) {
      console.log('✅ 按需导入测试通过');
    }

    // 测试直接模块导入
    const authModule = await import('./auth.js');
    const cartModule = await import('./cart.js');

    if (authModule.login && cartModule.getCartList) {
      console.log('✅ 直接模块导入测试通过');
    }

    return true;
  } catch (error) {
    console.error('❌ 导入测试失败:', error);
    return false;
  }
};

// 验证函数是否存在
const validateFunctions = async () => {
  const tests = [
    // 认证模块
    { module: 'auth', functions: ['login', 'register', 'logout'] },

    // 购物车模块
    {
      module: 'cart',
      functions: [
        'getCartList',
        'getMiniCart',
        'addToCart',
        'updateCart',
        'selectCartItems',
        'unselectCartItems',
        'removeCartItem',
      ],
    },

    // 账户模块
    {
      module: 'account',
      functions: [
        'getAccountInfo',
        'getUserInfo',
        'updateUserInfo',
        'updatePassword',
        'getUserOrders',
      ],
    },

    // 地址模块
    {
      module: 'address',
      functions: [
        'getAddressList',
        'getAddressDetail',
        'createAddress',
        'updateAddress',
        'deleteAddress',
      ],
    },

    // 其他模块
    { module: 'home', functions: ['getHomeInfo', 'getLatestProducts'] },
    { module: 'product', functions: ['getProductDetail'] },
    {
      module: 'wishlist',
      functions: ['getWishlist', 'addToWishlist', 'removeFromWishlist'],
    },
    {
      module: 'checkout',
      functions: ['getCheckoutInfo', 'updateCheckout', 'confirmOrder'],
    },
    {
      module: 'order',
      functions: ['getOrderDetail', 'getOrderPayInfo', 'cancelOrder'],
    },
  ];

  let allPassed = true;

  for (const test of tests) {
    try {
      const module = await import(`./${test.module}.js`);
      const missingFunctions = test.functions.filter(
        (fn) => typeof module[fn] !== 'function'
      );

      if (missingFunctions.length === 0) {
        console.log(
          `✅ ${test.module} 模块验证通过 (${test.functions.length} 个函数)`
        );
      } else {
        console.error(`❌ ${test.module} 模块缺少函数:`, missingFunctions);
        allPassed = false;
      }
    } catch (error) {
      console.error(`❌ ${test.module} 模块加载失败:`, error.message);
      allPassed = false;
    }
  }

  return allPassed;
};

// 验证文档是否存在（浏览器环境的简化版本）
const validateDocumentation = () => {
  console.log('📝 验证文档完整性...');

  const requiredDocs = ['README.md', 'quick-start.md', 'best-practices.md'];

  // 在浏览器环境中，我们只能提示检查文档
  console.log('📋 请确保以下文档文件存在：');
  requiredDocs.forEach((doc) => {
    console.log(`   - src/api/${doc}`);
  });
};

// 模拟 API 调用测试（不实际发送请求）
const simulateApiCalls = async () => {
  console.log('🧪 模拟 API 调用测试...');

  // 这里只是验证函数签名，不实际调用
  const testScenarios = [
    { name: '用户登录', module: 'auth', fn: 'login' },
    { name: '获取购物车', module: 'cart', fn: 'getCartList' },
    { name: '添加商品到购物车', module: 'cart', fn: 'addToCart' },
    { name: '获取用户信息', module: 'account', fn: 'getAccountInfo' },
  ];

  for (const scenario of testScenarios) {
    try {
      const module = await import(`./${scenario.module}.js`);
      if (typeof module[scenario.fn] === 'function') {
        console.log(`✅ ${scenario.name} - 函数签名正确`);
      } else {
        console.error(`❌ ${scenario.name} - 函数签名错误`);
      }
    } catch (error) {
      console.error(`❌ ${scenario.name} - 测试失败:`, error.message);
    }
  }
};

// 主测试函数
const runTests = async () => {
  console.log('🔍 API 验证开始...\n');

  try {
    // 1. 测试导入
    const importSuccess = await testImports();

    // 2. 验证函数存在性
    const functionsValid = await validateFunctions();

    // 3. 验证文档
    validateDocumentation();

    // 4. 模拟调用测试
    await simulateApiCalls();

    // 总结
    console.log('\n📊 验证总结:');
    if (importSuccess && functionsValid) {
      console.log('🎉 所有 API 模块验证通过！');
      console.log('💡 你现在可以在项目中安全使用这些 API 了');
      console.log('📖 查看 quick-start.md 开始使用');
    } else {
      console.log('⚠️  部分验证失败，请检查上述错误信息');
    }
  } catch (error) {
    console.error('💥 验证过程出现异常:', error);
  }
};

// 导出供其他地方使用
export { runTests, validateFunctions };

// 浏览器控制台快速测试
if (typeof window !== 'undefined') {
  window.testAPI = runTests;
  console.log('💡 在浏览器控制台运行 testAPI() 来验证 API');
}
