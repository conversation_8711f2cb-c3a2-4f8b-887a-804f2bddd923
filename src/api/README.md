# API 接口使用说明

本项目已将所有 API 接口按功能模块进行分类整理，方便在各个页面中导入使用。

## 📁 文件结构

```
src/api/
├── auth.js        # 认证相关接口（登录、注册、登出）
├── home.js        # 首页相关接口
├── product.js     # 商品相关接口
├── cart.js        # 购物车相关接口
├── checkout.js    # 结算相关接口
├── order.js       # 订单相关接口
├── account.js     # 账户信息相关接口
├── address.js     # 地址管理相关接口
├── wishlist.js    # 收藏相关接口
└── index.js       # 统一导出文件
```

## 🚀 使用方式

### 方式一：按模块导入

```javascript
// 导入整个模块
import { authAPI, cartAPI, accountAPI } from '@api/index';

// 使用
const loginUser = async () => {
  const result = await authAPI.login({ email, password });
};

const getCartData = async () => {
  const result = await cartAPI.getCartList();
};
```

### 方式二：按需导入

```javascript
// 按需导入具体函数
import { login, register } from '@api/auth';
import { getCartList, addToCart } from '@api/cart';
import { getAccountInfo } from '@api/account';

// 使用
const loginUser = async () => {
  const result = await login({ email, password });
};

const addProductToCart = async () => {
  const result = await addToCart({ sku_id: 123, quantity: 1 });
};
```

### 方式三：从统一入口导入

```javascript
// 从 index.js 统一导入
import { login, getCartList, getAccountInfo } from '@api/index';

// 使用
const handleLogin = async () => {
  try {
    const result = await login({
      email: '<EMAIL>',
      password: '123456',
    });
    console.log('登录成功:', result);
  } catch (error) {
    console.error('登录失败:', error);
  }
};
```

## 📖 各模块接口说明

### 🔐 认证模块 (auth.js)

- `login(data)` - 用户登录
- `register(data)` - 用户注册
- `logout()` - 用户登出

### 🏠 首页模块 (home.js)

- `getHomeInfo()` - 获取首页信息
- `getLatestProducts()` - 获取最新商品

### 📦 商品模块 (product.js)

- `getProductDetail(productId)` - 获取商品详情

### 🛒 购物车模块 (cart.js)

- `getCartList()` - 获取购物车列表
- `getMiniCart()` - 获取迷你购物车信息
- `addToCart(data)` - 添加商品到购物车
- `updateCart(cartId, data)` - 更新购物车商品
- `selectCartItems(cartIds)` - 选中购物车商品
- `unselectCartItems(cartIds)` - 取消选中购物车商品
- `removeCartItem(cartId)` - 删除购物车商品

### 💳 结算模块 (checkout.js)

- `getCheckoutInfo()` - 获取结算页信息
- `updateCheckout(data)` - 更新结算信息
- `confirmOrder()` - 确认订单
- `getCheckoutSuccess(orderNumber)` - 结算成功页

### 📋 订单模块 (order.js)

- `getOrderDetail(orderNumber)` - 获取订单详情
- `getOrderPayInfo(orderNumber)` - 获取订单支付信息
- `cancelOrder(orderNumber)` - 取消订单
- `completeOrder(orderNumber)` - 完成订单

### 👤 账户模块 (account.js)

- `getAccountInfo()` - 获取账户信息
- `getUserInfo()` - 获取用户信息
- `updateUserInfo(data)` - 更新用户信息
- `updatePassword(data)` - 修改密码
- `getUserOrders()` - 获取用户订单列表
- `getUserOrderDetail(orderNumber)` - 获取用户订单详情
- 售后相关接口...

### 📍 地址模块 (address.js)

- `getAddressList()` - 获取地址列表
- `getAddressDetail(addressId)` - 获取单个地址详情
- `createAddress(data)` - 创建新地址
- `updateAddress(addressId, data)` - 更新地址
- `deleteAddress(addressId)` - 删除地址

### ❤️ 收藏模块 (wishlist.js)

- `getWishlist()` - 获取收藏列表
- `addToWishlist(productId)` - 添加商品到收藏
- `removeFromWishlist(wishlistId)` - 删除收藏商品

## ⚡ 实际使用示例

### 登录页面示例

```javascript
// src/pages/Login/index.vue
import { login, register } from '@api/auth';

const doLogin = async () => {
  try {
    const result = await login({
      email: login.email,
      password: login.password,
    });
    router.replace('/');
    console.log('登录成功:', result);
  } catch (error) {
    ElMessage.error('登录失败');
  }
};
```

### 购物车页面示例

```javascript
// src/pages/Cart/index.vue
import { getCartList, updateCart, removeCartItem } from '@api/cart';

const loadCartData = async () => {
  try {
    const result = await getCartList();
    cartList.value = result.carts;
  } catch (error) {
    ElMessage.error('获取购物车数据失败');
  }
};

const updateCartItem = async (cartId, quantity) => {
  try {
    await updateCart(cartId, { quantity });
    await loadCartData(); // 重新加载数据
  } catch (error) {
    ElMessage.error('更新失败');
  }
};
```

## 🔧 注意事项

1. 所有接口都已经过请求拦截器处理，包含了统一的错误处理和 token 添加
2. 接口返回的数据已经过响应拦截器处理，直接返回 `data` 字段
3. 建议在使用时加上 try-catch 进行错误处理
4. API 接口路径严格按照 `api.md` 文档定义
