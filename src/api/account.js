import { get, post, put } from '@utils/request';

/**
 * 获取账户信息
 * @returns {Promise}
 */
export const getAccountInfo = () => {
  return get('/v1/account');
};

/**
 * 获取用户信息（编辑页面）
 * @returns {Promise}
 */
export const getUserInfo = () => {
  return get('/v1/account/edit');
};

/**
 * 更新用户信息
 * @param {Object} data - 用户信息
 * @param {string} data.name - 用户名
 * @param {string} data.email - 邮箱
 * @param {string} data.avatar - 头像
 * @returns {Promise}
 */
export const updateUserInfo = (data) => {
  return put('/v1/account/edit', data);
};

/**
 * 获取密码管理页面
 * @returns {Promise}
 */
export const getPasswordPage = () => {
  return get('/v1/account/password');
};

/**
 * 修改密码
 * @param {Object} data - 密码信息
 * @returns {Promise}
 */
export const updatePassword = (data) => {
  return post('/v1/account/password', data);
};

/**
 * 修改密码（另一个接口）
 * @returns {Promise}
 */
export const getUpdatePasswordPage = () => {
  return get('/v1/account/update_password');
};

/**
 * 获取用户订单列表
 * @returns {Promise}
 */
export const getUserOrders = () => {
  return get('/v1/account/orders');
};

/**
 * 获取用户订单详情
 * @param {string} orderNumber - 订单号
 * @returns {Promise}
 */
export const getUserOrderDetail = (orderNumber) => {
  return get(`/v1/account/orders/${orderNumber}`);
};

/**
 * 获取售后列表
 * @returns {Promise}
 */
export const getRMAList = () => {
  return get('/v1/account/rmas');
};

/**
 * 获取售后详情
 * @param {number} id - 售后ID
 * @returns {Promise}
 */
export const getRMADetail = (id) => {
  return get(`/v1/account/rmas/${id}`);
};

/**
 * 创建售后申请页面
 * @param {number} orderProductId - 订单商品ID
 * @returns {Promise}
 */
export const createRMAPage = (orderProductId) => {
  return get(`/v1/account/rmas/create/${orderProductId}`);
};

/**
 * 提交售后申请
 * @param {Object} data - 售后信息
 * @returns {Promise}
 */
export const submitRMA = (data) => {
  return post('/v1/account/rmas/store', data);
};
