import { get, post, del } from '@utils/request';

/**
 * 获取收藏列表
 * @returns {Promise}
 */
export const getWishlist = () => {
  return get('/v1/account/wishlist');
};

/**
 * 添加商品到收藏
 * @param {number} productId - 商品ID
 * @returns {Promise}
 */
export const addToWishlist = (productId) => {
  return post('/v1/account/wishlist', { product_id: productId });
};

/**
 * 删除收藏商品
 * @param {number} wishlistId - 收藏ID
 * @returns {Promise}
 */
export const removeFromWishlist = (wishlistId) => {
  return del(`/v1/account/wishlist/${wishlistId}`);
};
