import { get, post } from '@utils/request';

/**
 * 获取订单详情（公共接口）
 * @param {string} orderNumber - 订单号
 * @returns {Promise}
 */
export const getOrderDetail = (orderNumber) => {
  return get(`/orders/${orderNumber}`);
};

/**
 * 获取订单支付信息
 * @param {string} orderNumber - 订单号
 * @returns {Promise}
 */
export const getOrderPayInfo = (orderNumber) => {
  return get(`/v1/orders/${orderNumber}/pay`);
};

/**
 * 取消订单
 * @param {string} orderNumber - 订单号
 * @returns {Promise}
 */
export const cancelOrder = (orderNumber) => {
  return post(`/v1/orders/${orderNumber}/cancel`);
};

/**
 * 完成订单
 * @param {string} orderNumber - 订单号
 * @returns {Promise}
 */
export const completeOrder = (orderNumber) => {
  return post(`/v1/orders/${orderNumber}/complete`);
};
