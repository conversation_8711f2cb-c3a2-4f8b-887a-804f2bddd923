{"name": "web3", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "element-plus": "^2.10.4", "pinia": "^3.0.3", "sass": "^1.89.2", "swiper": "^11.2.10", "vue": "^3.5.17", "vue-i18n": "^12.0.0-alpha.3", "vue-router": "4"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "vite": "^7.0.4"}}